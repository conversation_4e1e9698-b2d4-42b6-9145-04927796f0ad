/// Model class representing a product in the inventory system
class Product {
  /// Unique identifier for the product
  int? id;

  /// Name of the product
  String name;

  /// Category ID that this product belongs to
  int? categoryId;

  /// Unit ID for measuring this product
  int? unitId;

  /// Price of the product
  double? price;

  /// Current quantity in stock
  double? quantity;

  /// Supplier ID for this product
  int? supplierId;

  /// Description of the product
  String? description;

  /// Barcode of the product
  String? barcode;

  /// Constructor for creating a Product instance
  Product({
    this.id,
    required this.name,
    this.categoryId,
    this.unitId,
    this.price,
    this.quantity,
    this.supplierId,
    this.description,
    this.barcode,
  });

  /// Converts the Product instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'categoryId': categoryId,
      'unitId': unitId,
      'price': price,
      'quantity': quantity,
      'supplierId': supplierId,
      'description': description,
      'barcode': barcode,
    };
  }

  /// Creates a Product instance from a Map (typically from database)
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'] as int?,
      name: map['name'] as String? ?? '',
      categoryId: map['categoryId'] as int?,
      unitId: map['unitId'] as int?,
      price: map['price']?.toDouble(),
      quantity: map['quantity']?.toDouble(),
      supplierId: map['supplierId'] as int?,
      description: map['description'] as String?,
      barcode: map['barcode'] as String?,
    );
  }

  @override
  String toString() {
    return 'Product{id: $id, name: $name, categoryId: $categoryId, '
        'unitId: $unitId, price: $price, quantity: $quantity, '
        'supplierId: $supplierId, description: $description}';
  }
}
