import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:inventory_management_app/data/database_helper.dart';

class DatabaseService {
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initializeDatabase();
    return _database!;
  }

  Future<Database> _initializeDatabase() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, 'inventory_management.db');

    return openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute(createProductsTable);
    await db.execute(createCustomersTable);
    await db.execute(createSuppliersTable);
    await db.execute(createCategoriesTable);
    await db.execute(createUnitsTable);
    await db.execute(createOrdersTable);
    await db.execute(createOrderItemsTable);
    await db.execute(createSalesTable);
    await db.execute(createSaleItemsTable);
    await db.execute(createPurchasesTable);
    await db.execute(createPurchaseItemsTable);
    await db.execute(createExpensesTable);
    await db.execute(createBackupsTable);
    await db.execute(createActivitiesTable);
    await db.execute(createTransactionsTable);
    await db.execute(createDailySummaryTable);
    await db.execute(createCustomerStatementTable);
    await db.execute(createSupplierStatementTable);
  }
}
