import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/widgets/empty_state_widget.dart' as widgets;
import 'package:inventory_management_app/widgets/confirmation_dialog.dart';

/// شاشة الأصناف الغذائية
class ArabicProductsScreen extends StatefulWidget {
  const ArabicProductsScreen({super.key});

  @override
  State<ArabicProductsScreen> createState() => _ArabicProductsScreenState();
}

class _ArabicProductsScreenState extends State<ArabicProductsScreen> {
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  final List<String> _categories = <String>[
    'الكل',
    'منتجات الألبان',
    'المشروبات',
    'المعلبات',
    'الحلويات',
    'منتجات التنظيف',
    'المجمدات',
    'المخبوزات',
    'البهارات',
    'الزيوت',
    'الحبوب',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).fetchProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'الأصناف الغذائية',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.orange[700],
          foregroundColor: Colors.white,
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => context.go('/products/add'),
              tooltip: 'إضافة صنف جديد',
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _showSearchDialog(),
              tooltip: 'البحث',
            ),
          ],
        ),
        body: Column(
          children: <Widget>[
            // شريط التصفية
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[100],
              child: Row(
                children: <Widget>[
                  const Text(
                    'الفئة:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButton<String>(
                      value: _selectedCategory,
                      isExpanded: true,
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedCategory = newValue!;
                        });
                      },
                      items: _categories.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: const TextStyle(fontFamily: 'Cairo'),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
            
            // قائمة الأصناف
            Expanded(
              child: Consumer<ProductProvider>(
                builder: (BuildContext context, ProductProvider productProvider, Widget? child) {
                  if (productProvider.isLoading) {
                    return const widgets.LoadingWidget(
                      message: 'جاري تحميل الأصناف الغذائية...',
                    );
                  }

                  if (productProvider.error != null) {
                    return widgets.ErrorWidget(
                      message: productProvider.error!,
                      onRetry: () => productProvider.fetchProducts(),
                    );
                  }

                  final List<Product> filteredProducts = _filterProducts(productProvider.products);

                  if (filteredProducts.isEmpty) {
                    return const widgets.EmptyStateWidget(
                      title: 'لا توجد أصناف غذائية',
                      message: 'ابدأ بإضافة أول صنف غذائي لمحلك',
                      icon: Icons.fastfood,
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredProducts.length,
                    itemBuilder: (BuildContext context, int index) {
                      final Product product = filteredProducts[index];
                      return _buildProductCard(context, product, productProvider);
                    },
                  );
                },
              ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => context.go('/products/add'),
          backgroundColor: Colors.orange[700],
          child: const Icon(Icons.add, color: Colors.white),
          tooltip: 'إضافة صنف جديد',
        ),
      ),
    );
  }

  List<Product> _filterProducts(List<Product> products) {
    return products.where((Product product) {
      final bool matchesSearch = _searchQuery.isEmpty ||
          product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (product.description.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      
      final bool matchesCategory = _selectedCategory == 'الكل' ||
          product.categoryId.toString() == _selectedCategory; // تحتاج تحسين
      
      return matchesSearch && matchesCategory;
    }).toList();
  }

  Widget _buildProductCard(BuildContext context, Product product, ProductProvider provider) {
    final bool isLowStock = product.quantity < 10;
    final bool isExpired = false; // تحتاج تحديد منطق انتهاء الصلاحية

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => context.go('/products/details/${product.id}'),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          product.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Cairo',
                          ),
                        ),
                        ...[
                        const SizedBox(height: 4),
                        Text(
                          product.description!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ],
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: <Widget>[
                      Text(
                        '${product.price.toStringAsFixed(2)} ر.س',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isLowStock ? Colors.red[100] : Colors.green[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'الكمية: ${product.quantity.toInt()}',
                          style: TextStyle(
                            fontSize: 12,
                            color: isLowStock ? Colors.red[700] : Colors.green[700],
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // أزرار العمليات
              Row(
                children: <Widget>[
                  if (isLowStock)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Icon(Icons.warning, size: 16, color: Colors.orange[700]),
                          const SizedBox(width: 4),
                          Text(
                            'مخزون منخفض',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange[700],
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  const Spacer(),
                  
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.blue),
                    onPressed: () => context.go('/products/edit/${product.id}'),
                    tooltip: 'تعديل',
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _deleteProduct(context, product, provider),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text(
            'البحث في الأصناف',
            style: TextStyle(fontFamily: 'Cairo'),
          ),
          content: TextField(
            autofocus: true,
            decoration: const InputDecoration(
              hintText: 'ادخل اسم الصنف...',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: (String value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                setState(() {
                  _searchQuery = '';
                });
                Navigator.pop(context);
              },
              child: const Text('مسح', style: TextStyle(fontFamily: 'Cairo')),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق', style: TextStyle(fontFamily: 'Cairo')),
            ),
          ],
        ),
      ),
    );
  }

  void _deleteProduct(BuildContext context, Product product, ProductProvider provider) async {
    final bool? confirmed = await ConfirmationDialog.showDeleteConfirmation(
      context,
      itemName: 'الصنف "${product.name}"',
    );
    
    if (confirmed == true) {
      await provider.deleteProduct(product.id!);
      if (context.mounted) {
        SnackBarUtils.showSuccess(context, 'تم حذف الصنف بنجاح');
      }
    }
  }
}
