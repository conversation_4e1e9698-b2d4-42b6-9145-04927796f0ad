import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/category.dart';
import 'package:inventory_management_app/services/database_service.dart';

class CategoryProvider extends ChangeNotifier {
  List<Category> _categories = [];
  final _databaseService = DatabaseService();

  List<Category> get categories => _categories;

  Future<void> fetchCategories() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('categories');
    _categories = List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addCategory(Category category) async {
    final db = await _databaseService.database;
    await db.insert('categories', category.toMap());
    await fetchCategories();
  }

  Future<void> updateCategory(Category category) async {
    final db = await _databaseService.database;
    await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
    await fetchCategories();
  }

  Future<void> deleteCategory(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchCategories();
  }
}
