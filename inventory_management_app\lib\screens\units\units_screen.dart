import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/unit_provider.dart';
import 'package:inventory_management_app/models/unit.dart';
import 'package:inventory_management_app/screens/units/unit_details_screen.dart';

class UnitsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final unitProvider = Provider.of<UnitProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Units'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => UnitDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: unitProvider.units.length,
        itemBuilder: (context, index) {
          final unit = unitProvider.units[index];
          return Card(
            child: ListTile(
              title: Text(unit.name),
              subtitle: Text(unit.symbol),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => UnitDetailsScreen(unit: unit),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      unitProvider.deleteUnit(unit.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
