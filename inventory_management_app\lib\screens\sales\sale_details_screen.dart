import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/models/sale_item.dart';
import 'package:sqflite_common/sqlite_api.dart';

class SaleDetailsScreen extends StatefulWidget {
  final Sale sale;

  SaleDetailsScreen({required this.sale});

  @override
  _SaleDetailsScreenState createState() => _SaleDetailsScreenState();
}

class _SaleDetailsScreenState extends State<SaleDetailsScreen> {
  List<SaleItem> _saleItems = <SaleItem>[];

  @override
  void initState() {
    super.initState();
    _fetchSaleItems();
  }

  Future<void> _fetchSaleItems() async {
    final Database db = await DatabaseService().database;
    final List<Map<String, dynamic>> maps = await db.query(
      'sale_items',
      where: 'saleId = ?',
      whereArgs: <Object?>[widget.sale.id],
    );
    setState(() {
      _saleItems = List.generate(maps.length, (int i) {
        return SaleItem.fromMap(maps[i]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sale Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('Sale ID: ${widget.sale.id}'),
            Text('Customer ID: ${widget.sale.customerId}'),
            Text('Sale Date: ${widget.sale.saleDate}'),
            Text(
                'Total Amount: \$${widget.sale.totalAmount.toStringAsFixed(2)}'),
            const SizedBox(height: 16),
            const Text(
              'Sale Items:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: _saleItems.length,
                itemBuilder: (BuildContext context, int index) {
                  final SaleItem saleItem = _saleItems[index];
                  return ListTile(
                    title: Text('Product ID: ${saleItem.productId}'),
                    subtitle: Text(
                        'Quantity: ${saleItem.quantity}, Price: \$${saleItem.price.toStringAsFixed(2)}'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
