import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/expense.dart';
import 'package:inventory_management_app/services/database_service.dart';

class ExpenseProvider extends ChangeNotifier {
  List<Expense> _expenses = [];
  final _databaseService = DatabaseService();

  List<Expense> get expenses => _expenses;

  Future<void> fetchExpenses() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('expenses');
    _expenses = List.generate(maps.length, (i) {
      return Expense.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addExpense(Expense expense) async {
    final db = await _databaseService.database;
    await db.insert('expenses', expense.toMap());
    await fetchExpenses();
  }

  Future<void> updateExpense(Expense expense) async {
    final db = await _databaseService.database;
    await db.update(
      'expenses',
      expense.toMap(),
      where: 'id = ?',
      whereArgs: [expense.id],
    );
    await fetchExpenses();
  }

  Future<void> deleteExpense(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'expenses',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchExpenses();
  }
}
