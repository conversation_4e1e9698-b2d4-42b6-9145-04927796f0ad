import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/supplier.dart';
import 'package:inventory_management_app/services/database_service.dart';

class SupplierProvider extends ChangeNotifier {
  List<Supplier> _suppliers = [];
  final _databaseService = DatabaseService();

  List<Supplier> get suppliers => _suppliers;

  Future<void> fetchSuppliers() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('suppliers');
    _suppliers = List.generate(maps.length, (i) {
      return Supplier.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addSupplier(Supplier supplier) async {
    final db = await _databaseService.database;
    await db.insert('suppliers', supplier.toMap());
    await fetchSuppliers();
  }

  Future<void> updateSupplier(Supplier supplier) async {
    final db = await _databaseService.database;
    await db.update(
      'suppliers',
      supplier.toMap(),
      where: 'id = ?',
      whereArgs: [supplier.id],
    );
    await fetchSuppliers();
  }

  Future<void> deleteSupplier(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'suppliers',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchSuppliers();
  }
}
