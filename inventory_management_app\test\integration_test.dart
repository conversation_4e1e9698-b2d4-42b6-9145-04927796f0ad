import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/category.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/models/sale_item.dart';
import 'package:inventory_management_app/services/business_logic_service.dart';
import 'package:inventory_management_app/services/transaction_service.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/category_service.dart';
import 'package:inventory_management_app/providers/product_provider.dart';

void main() {
  // Initialize FFI
  sqfliteFfiInit();
  
  group('Integration Tests', () {
    late BusinessLogicService businessLogic;
    late TransactionService transactionService;
    late ProductService productService;
    late CategoryService categoryService;
    late ProductProvider productProvider;

    setUpAll(() {
      // Use the ffi factory for testing
      databaseFactory = databaseFactoryFfi;
      businessLogic = BusinessLogicService();
      transactionService = TransactionService();
      productService = ProductService();
      categoryService = CategoryService();
      productProvider = ProductProvider();
    });

    group('Business Logic Tests', () {
      test('should validate product name uniqueness', () async {
        // Create a category
        final category = Category(name: 'Electronics');
        final categoryId = await categoryService.insertCategory(category);

        // Create a product
        final product = Product(
          name: 'iPhone',
          categoryId: categoryId,
          price: 999.0,
          quantity: 10.0,
        );
        await productService.insertProduct(product);

        // Test uniqueness validation
        final isUnique = await businessLogic.isProductNameUniqueInCategory(
            'iPhone', categoryId);
        expect(isUnique, isFalse);

        final isUniqueNew = await businessLogic.isProductNameUniqueInCategory(
            'Samsung Galaxy', categoryId);
        expect(isUniqueNew, isTrue);
      });

      test('should check deletion constraints', () async {
        // Create category with product
        final category = Category(name: 'Test Category');
        final categoryId = await categoryService.insertCategory(category);

        final product = Product(
          name: 'Test Product',
          categoryId: categoryId,
          price: 100.0,
          quantity: 5.0,
        );
        await productService.insertProduct(product);

        // Should not be able to delete category with products
        final canDelete = await businessLogic.canDeleteCategory(categoryId);
        expect(canDelete, isFalse);
      });
    });

    group('Transaction Tests', () {
      test('should create sale with items and update stock', () async {
        // Create product
        final product = Product(
          name: 'Test Product for Sale',
          price: 50.0,
          quantity: 20.0,
        );
        final productId = await productService.insertProduct(product);

        // Create sale with items
        final sale = Sale(
          customerId: 1,
          date: DateTime.now().toIso8601String(),
          total: 100.0,
        );

        final saleItems = [
          SaleItem(
            productId: productId,
            quantity: 2.0,
            price: 50.0,
          ),
        ];

        // Execute transaction
        final saleId = await transactionService.createSaleWithItems(
            sale, saleItems);
        expect(saleId, isPositive);

        // Verify stock was updated
        final updatedProduct = await productService.getProductById(productId);
        expect(updatedProduct?.quantity, equals(18.0));
      });

      test('should handle insufficient stock error', () async {
        // Create product with low stock
        final product = Product(
          name: 'Low Stock Product',
          price: 30.0,
          quantity: 1.0,
        );
        final productId = await productService.insertProduct(product);

        // Try to sell more than available
        final sale = Sale(
          customerId: 1,
          date: DateTime.now().toIso8601String(),
          total: 60.0,
        );

        final saleItems = [
          SaleItem(
            productId: productId,
            quantity: 5.0, // More than available
            price: 30.0,
          ),
        ];

        // Should throw exception
        expect(
          () => transactionService.createSaleWithItems(sale, saleItems),
          throwsException,
        );
      });
    });

    group('Provider Integration Tests', () {
      test('should handle provider operations with error handling', () async {
        // Test product provider operations
        await productProvider.fetchProducts();
        expect(productProvider.isLoading, isFalse);
        expect(productProvider.error, isNull);

        // Test adding product
        final newProduct = Product(
          name: 'Provider Test Product',
          price: 25.0,
          quantity: 15.0,
        );

        await productProvider.addProduct(newProduct);
        expect(productProvider.error, isNull);

        // Test validation
        final isValid = await productProvider.validateProductName(
            'Provider Test Product', null);
        expect(isValid, isFalse); // Should be false as it already exists
      });

      test('should handle provider error scenarios', () async {
        // Test error handling by trying to delete non-existent product
        await productProvider.deleteProduct(99999);
        // Should not crash, error should be handled gracefully
        expect(productProvider.error, isNull); // Delete of non-existent is OK
      });
    });

    group('Performance Tests', () {
      test('should handle bulk operations efficiently', () async {
        final stopwatch = Stopwatch()..start();

        // Create multiple products
        for (int i = 0; i < 100; i++) {
          final product = Product(
            name: 'Bulk Product $i',
            price: 10.0 + i,
            quantity: 100.0,
          );
          await productService.insertProduct(product);
        }

        stopwatch.stop();
        print('Bulk insert time: ${stopwatch.elapsedMilliseconds}ms');

        // Should complete within reasonable time (adjust as needed)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));

        // Verify all products were created
        final products = await productService.getAllProducts();
        expect(products.length, greaterThanOrEqualTo(100));
      });
    });
  });
}
