import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:go_router/go_router.dart';

class HomeScreen extends StatefulWidget {
  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/suppliers');
        break;
      case 4:
        context.go('/orders');
        break;
      case 5:
        context.go('/sales');
        break;
      case 6:
        context.go('/purchases');
        break;
      case 7:
        context.go('/expenses');
        break;
      case 8:
        context.go('/categories');
        break;
      case 9:
        context.go('/units');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final productProvider = Provider.of<ProductProvider>(context);
    final orderProvider = Provider.of<OrderProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Inventory Management'),
      ),
      body: Column(
        children: [
          ElevatedButton(
            onPressed: () {
              context.go('/products');
            },
            child: Text('Go to Products'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/customers');
            },
            child: Text('Go to Customers'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/suppliers');
            },
            child: Text('Go to Suppliers'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/orders');
            },
            child: Text('Go to Orders'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/sales');
            },
            child: Text('Go to Sales'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/purchases');
            },
            child: Text('Go to Purchases'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/expenses');
            },
            child: Text('Go to Expenses'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/categories');
            },
            child: Text('Go to Categories'),
          ),
          ElevatedButton(
            onPressed: () {
              context.go('/units');
            },
            child: Text('Go to Units'),
          ),
          Text('Products:'),
          Expanded(
            child: ListView(
              padding: EdgeInsets.all(16),
              children: [
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Products',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: productProvider.products.length,
                          itemBuilder: (context, index) {
                            final product = productProvider.products[index];
                            return ListTile(
                              title: Text(product.name),
                              subtitle: Text(product.description),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Orders',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: orderProvider.orders.length,
                          itemBuilder: (context, index) {
                            final order = orderProvider.orders[index];
                            return ListTile(
                              title: Text('Order ID: ${order.id}'),
                              subtitle:
                                  Text('Customer ID: ${order.customerId}'),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Products',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Customers',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.local_shipping),
            label: 'Suppliers',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Orders',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.monetization_on),
            label: 'Sales',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag),
            label: 'Purchases',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.money_off),
            label: 'Expenses',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.category),
            label: 'Categories',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.view_module),
            label: 'Units',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
      ),
    );
  }
}
