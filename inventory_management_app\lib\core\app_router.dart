import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:inventory_management_app/screens/home/<USER>';
import 'package:inventory_management_app/screens/home/<USER>';
import 'package:inventory_management_app/screens/products/arabic_products_screen.dart';
import 'package:inventory_management_app/screens/products/products_screen.dart';
import 'package:inventory_management_app/screens/products/add_edit_product_screen.dart';
import 'package:inventory_management_app/screens/dashboard/dashboard_screen.dart';
import 'package:inventory_management_app/screens/customers/customers_screen.dart';
import 'package:inventory_management_app/screens/suppliers/suppliers_screen.dart';
import 'package:inventory_management_app/screens/orders/orders_screen.dart';
import 'package:inventory_management_app/screens/sales/sales_screen.dart';
import 'package:inventory_management_app/screens/purchases/purchases_screen.dart';
import 'package:inventory_management_app/screens/expenses/expenses_screen.dart';
import 'package:inventory_management_app/screens/categories/categories_screen.dart';
import 'package:inventory_management_app/screens/units/units_screen.dart';
import 'package:inventory_management_app/screens/backup/backup_screen.dart';
import 'package:inventory_management_app/screens/reports/reports_screen.dart';
import 'package:inventory_management_app/screens/settings/settings_screen.dart';

/// Application router configuration using go_router

class AppRouter {
  /// Main router instance with all application routes
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    routes: <RouteBase>[
      // Home route
      GoRoute(
        path: '/',
        name: 'home',
        builder: (BuildContext context, GoRouterState state) =>
            const ArabicHomeScreen(),
      ),

      // Dashboard route
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (BuildContext context, GoRouterState state) =>
            const DashboardScreen(),
      ),

      // Products routes with nested routes
      GoRoute(
        path: '/products',
        name: 'products',
        builder: (BuildContext context, GoRouterState state) =>
            const ArabicProductsScreen(),
        routes: <RouteBase>[
          GoRoute(
            path: '/add',
            name: 'products-add',
            builder: (BuildContext context, GoRouterState state) =>
                const AddEditProductScreen(),
          ),
          GoRoute(
            path: '/edit/:id',
            name: 'products-edit',
            builder: (BuildContext context, GoRouterState state) {
              final int? id = int.tryParse(state.pathParameters['id'] ?? '');
              return AddEditProductScreen(productId: id);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/customers',
        builder: (BuildContext context, GoRouterState state) =>
            CustomersScreen(),
      ),
      GoRoute(
        path: '/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            SuppliersScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (BuildContext context, GoRouterState state) => OrdersScreen(),
      ),
      GoRoute(
        path: '/sales',
        builder: (BuildContext context, GoRouterState state) => SalesScreen(),
      ),
      GoRoute(
        path: '/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            PurchasesScreen(),
      ),
      GoRoute(
        path: '/expenses',
        builder: (BuildContext context, GoRouterState state) =>
            ExpensesScreen(),
      ),
      GoRoute(
        path: '/categories',
        builder: (BuildContext context, GoRouterState state) =>
            CategoriesScreen(),
      ),
      GoRoute(
        path: '/units',
        builder: (BuildContext context, GoRouterState state) => UnitsScreen(),
      ),

      // Reports Route
      GoRoute(
        path: '/reports',
        name: 'reports',
        builder: (BuildContext context, GoRouterState state) =>
            const ReportsScreen(),
      ),

      // Backup Route
      GoRoute(
        path: '/backup',
        name: 'backup',
        builder: (BuildContext context, GoRouterState state) =>
            const BackupScreen(),
      ),

      // Settings Route
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (BuildContext context, GoRouterState state) =>
            const SettingsScreen(),
      ),
    ],
  );
}
