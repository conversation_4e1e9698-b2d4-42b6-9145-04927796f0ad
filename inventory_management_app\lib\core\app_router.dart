import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:inventory_management_app/screens/home/<USER>';
import 'package:inventory_management_app/screens/products/products_screen.dart';
import 'package:inventory_management_app/screens/customers/customers_screen.dart';
import 'package:inventory_management_app/screens/suppliers/suppliers_screen.dart';
import 'package:inventory_management_app/screens/orders/orders_screen.dart';
import 'package:inventory_management_app/screens/sales/sales_screen.dart';
import 'package:inventory_management_app/screens/purchases/purchases_screen.dart';
import 'package:inventory_management_app/screens/expenses/expenses_screen.dart';
import 'package:inventory_management_app/screens/categories/categories_screen.dart';
import 'package:inventory_management_app/screens/units/units_screen.dart';
import 'package:inventory_management_app/screens/backup/backup_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        builder: (BuildContext context, GoRouterState state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/products',
        builder: (BuildContext context, GoRouterState state) =>
            const ProductsScreen(),
      ),
      GoRoute(
        path: '/customers',
        builder: (BuildContext context, GoRouterState state) =>
            CustomersScreen(),
      ),
      GoRoute(
        path: '/suppliers',
        builder: (BuildContext context, GoRouterState state) =>
            SuppliersScreen(),
      ),
      GoRoute(
        path: '/orders',
        builder: (BuildContext context, GoRouterState state) => OrdersScreen(),
      ),
      GoRoute(
        path: '/sales',
        builder: (BuildContext context, GoRouterState state) => SalesScreen(),
      ),
      GoRoute(
        path: '/purchases',
        builder: (BuildContext context, GoRouterState state) =>
            PurchasesScreen(),
      ),
      GoRoute(
        path: '/expenses',
        builder: (BuildContext context, GoRouterState state) =>
            ExpensesScreen(),
      ),
      GoRoute(
        path: '/categories',
        builder: (BuildContext context, GoRouterState state) =>
            CategoriesScreen(),
      ),
      GoRoute(
        path: '/units',
        builder: (BuildContext context, GoRouterState state) => UnitsScreen(),
      ),
      GoRoute(
        path: '/backup',
        builder: (BuildContext context, GoRouterState state) => BackupScreen(),
      ),
    ],
  );
}
