import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/unit.dart';
import 'package:inventory_management_app/services/database_service.dart';

class UnitProvider extends ChangeNotifier {
  List<Unit> _units = [];
  final _databaseService = DatabaseService();

  List<Unit> get units => _units;

  Future<void> fetchUnits() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('units');
    _units = List.generate(maps.length, (i) {
      return Unit.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addUnit(Unit unit) async {
    final db = await _databaseService.database;
    await db.insert('units', unit.toMap());
    await fetchUnits();
  }

  Future<void> updateUnit(Unit unit) async {
    final db = await _databaseService.database;
    await db.update(
      'units',
      unit.toMap(),
      where: 'id = ?',
      whereArgs: [unit.id],
    );
    await fetchUnits();
  }

  Future<void> deleteUnit(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'units',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchUnits();
  }
}
