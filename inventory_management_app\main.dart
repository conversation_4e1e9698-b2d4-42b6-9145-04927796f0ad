import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/core/app_router.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/providers/category_provider.dart';
import 'package:inventory_management_app/providers/unit_provider.dart';
import 'package:inventory_management_app/utils/theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
        ChangeNotifierProvider(create: (_) => CustomerProvider()),
        ChangeNotifierProvider(create: (_) => SupplierProvider()),
        ChangeNotifierProvider(create: (_) => SaleProvider()),
        ChangeNotifierProvider(create: (_) => PurchaseProvider()),
        ChangeNotifierProvider(create: (_) => ExpenseProvider()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => UnitProvider()),
      ],
      child: MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    Provider.of<ProductProvider>(context, listen: false).fetchProducts();
    Provider.of<OrderProvider>(context, listen: false).fetchOrders();
    Provider.of<CustomerProvider>(context, listen: false).fetchCustomers();
    Provider.of<SupplierProvider>(context, listen: false).fetchSuppliers();
    Provider.of<SaleProvider>(context, listen: false).fetchSales();
    Provider.of<PurchaseProvider>(context, listen: false).fetchPurchases();
    Provider.of<ExpenseProvider>(context, listen: false).fetchExpenses();
    Provider.of<CategoryProvider>(context, listen: false).fetchCategories();
    Provider.of<UnitProvider>(context, listen: false).fetchUnits();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: AppRouter.router,
      theme: AppTheme.lightTheme,
    );
  }
}
