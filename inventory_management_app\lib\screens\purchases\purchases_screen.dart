import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/screens/purchases/purchase_details_screen.dart';

class PurchasesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final purchaseProvider = Provider.of<PurchaseProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Purchases'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PurchaseDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: purchaseProvider.purchases.length,
        itemBuilder: (context, index) {
          final purchase = purchaseProvider.purchases[index];
          return Card(
            child: ListTile(
              title: Text('Purchase ID: ${purchase.id}'),
              subtitle: Text('Supplier ID: ${purchase.supplierId}'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              PurchaseDetailsScreen(purchase: purchase),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      purchaseProvider.deletePurchase(purchase.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
