class Backup {
  int? id;
  DateTime backupDate;
  String backupPath;

  Backup({
    this.id,
    required this.backupDate,
    required this.backupPath,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'backupDate': backupDate.toIso8601String(),
      'backupPath': backupPath,
    };
  }

  factory Backup.fromMap(Map<String, dynamic> map) {
    return Backup(
      id: map['id'],
      backupDate: DateTime.parse(map['backupDate']),
      backupPath: map['backupPath'],
    );
  }
}
