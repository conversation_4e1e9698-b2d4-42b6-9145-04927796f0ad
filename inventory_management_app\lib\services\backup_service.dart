import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:inventory_management_app/services/database_service.dart';

/// Service for handling backup and restore operations
class BackupService {
  static final BackupService _instance = BackupService._internal();
  factory BackupService() => _instance;
  BackupService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// Creates a backup of all data to JSON format
  Future<Map<String, dynamic>> createBackupData() async {
    try {
      final Database db = await _databaseService.database;
      
      // Get all data from tables
      final List<Map<String, dynamic>> products = await db.query('products');
      final List<Map<String, dynamic>> customers = await db.query('customers');
      final List<Map<String, dynamic>> suppliers = await db.query('suppliers');
      final List<Map<String, dynamic>> categories = await db.query('categories');
      final List<Map<String, dynamic>> units = await db.query('units');
      final List<Map<String, dynamic>> sales = await db.query('sales');
      final List<Map<String, dynamic>> purchases = await db.query('purchases');
      final List<Map<String, dynamic>> expenses = await db.query('expenses');
      final List<Map<String, dynamic>> orders = await db.query('orders');

      return <String, dynamic>{
        'backup_info': <String, dynamic>{
          'version': '1.0.0',
          'created_at': DateTime.now().toIso8601String(),
          'app_name': 'Inventory Management App',
        },
        'data': <String, dynamic>{
          'products': products,
          'customers': customers,
          'suppliers': suppliers,
          'categories': categories,
          'units': units,
          'sales': sales,
          'purchases': purchases,
          'expenses': expenses,
          'orders': orders,
        },
      };
    } catch (e) {
      throw Exception('Failed to create backup data: $e');
    }
  }

  /// Exports backup to a local file
  Future<String?> exportBackupToFile() async {
    try {
      // Create backup data
      final Map<String, dynamic> backupData = await createBackupData();
      final String jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      if (kIsWeb) {
        // For web platform, use file_picker to save
        final String fileName = 'inventory_backup_${DateTime.now().millisecondsSinceEpoch}.json';
        
        // This will trigger a download in web browsers
        final PlatformFile file = PlatformFile(
          name: fileName,
          size: jsonString.length,
          bytes: utf8.encode(jsonString),
        );
        
        return fileName;
      } else {
        // For mobile/desktop platforms
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Backup File',
          fileName: 'inventory_backup_${DateTime.now().millisecondsSinceEpoch}.json',
          type: FileType.custom,
          allowedExtensions: <String>['json'],
        );

        if (outputFile != null) {
          final File file = File(outputFile);
          await file.writeAsString(jsonString);
          return outputFile;
        }
      }
      
      return null;
    } catch (e) {
      throw Exception('Failed to export backup: $e');
    }
  }

  /// Imports backup from a local file
  Future<bool> importBackupFromFile() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: <String>['json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final File file = File(result.files.single.path!);
        final String jsonString = await file.readAsString();
        
        return await restoreFromBackupData(jsonString);
      }
      
      return false;
    } catch (e) {
      throw Exception('Failed to import backup: $e');
    }
  }

  /// Restores data from backup JSON string
  Future<bool> restoreFromBackupData(String jsonString) async {
    try {
      final Map<String, dynamic> backupData = json.decode(jsonString) as Map<String, dynamic>;
      
      // Validate backup format
      if (!backupData.containsKey('backup_info') || !backupData.containsKey('data')) {
        throw Exception('Invalid backup file format');
      }

      final Map<String, dynamic> data = backupData['data'] as Map<String, dynamic>;
      final Database db = await _databaseService.database;

      // Start transaction for data integrity
      await db.transaction((Transaction txn) async {
        // Clear existing data (optional - you might want to merge instead)
        await txn.delete('orders');
        await txn.delete('expenses');
        await txn.delete('purchases');
        await txn.delete('sales');
        await txn.delete('products');
        await txn.delete('customers');
        await txn.delete('suppliers');
        await txn.delete('categories');
        await txn.delete('units');

        // Restore data in correct order (respecting foreign key constraints)
        if (data.containsKey('categories')) {
          for (final Map<String, dynamic> item in data['categories'] as List<dynamic>) {
            await txn.insert('categories', item);
          }
        }

        if (data.containsKey('units')) {
          for (final Map<String, dynamic> item in data['units'] as List<dynamic>) {
            await txn.insert('units', item);
          }
        }

        if (data.containsKey('suppliers')) {
          for (final Map<String, dynamic> item in data['suppliers'] as List<dynamic>) {
            await txn.insert('suppliers', item);
          }
        }

        if (data.containsKey('customers')) {
          for (final Map<String, dynamic> item in data['customers'] as List<dynamic>) {
            await txn.insert('customers', item);
          }
        }

        if (data.containsKey('products')) {
          for (final Map<String, dynamic> item in data['products'] as List<dynamic>) {
            await txn.insert('products', item);
          }
        }

        if (data.containsKey('sales')) {
          for (final Map<String, dynamic> item in data['sales'] as List<dynamic>) {
            await txn.insert('sales', item);
          }
        }

        if (data.containsKey('purchases')) {
          for (final Map<String, dynamic> item in data['purchases'] as List<dynamic>) {
            await txn.insert('purchases', item);
          }
        }

        if (data.containsKey('expenses')) {
          for (final Map<String, dynamic> item in data['expenses'] as List<dynamic>) {
            await txn.insert('expenses', item);
          }
        }

        if (data.containsKey('orders')) {
          for (final Map<String, dynamic> item in data['orders'] as List<dynamic>) {
            await txn.insert('orders', item);
          }
        }
      });

      return true;
    } catch (e) {
      throw Exception('Failed to restore backup: $e');
    }
  }

  /// Creates an automatic backup to app documents directory
  Future<String?> createAutoBackup() async {
    try {
      final Map<String, dynamic> backupData = await createBackupData();
      final String jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      if (!kIsWeb) {
        final Directory appDocDir = await getApplicationDocumentsDirectory();
        final Directory backupDir = Directory('${appDocDir.path}/backups');
        
        if (!await backupDir.exists()) {
          await backupDir.create(recursive: true);
        }

        final String fileName = 'auto_backup_${DateTime.now().millisecondsSinceEpoch}.json';
        final File backupFile = File('${backupDir.path}/$fileName');
        
        await backupFile.writeAsString(jsonString);
        
        // Keep only last 5 auto backups
        await _cleanupOldBackups(backupDir);
        
        return backupFile.path;
      }
      
      return null;
    } catch (e) {
      throw Exception('Failed to create auto backup: $e');
    }
  }

  /// Cleans up old backup files, keeping only the latest 5
  Future<void> _cleanupOldBackups(Directory backupDir) async {
    try {
      final List<FileSystemEntity> files = backupDir.listSync()
          .where((FileSystemEntity entity) => entity is File && entity.path.endsWith('.json'))
          .toList();

      if (files.length > 5) {
        // Sort by modification time (newest first)
        files.sort((FileSystemEntity a, FileSystemEntity b) => 
            (b as File).lastModifiedSync().compareTo((a as File).lastModifiedSync()));

        // Delete old files (keep only first 5)
        for (int i = 5; i < files.length; i++) {
          await files[i].delete();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up old backups: $e');
      }
    }
  }

  /// Gets list of available auto backups
  Future<List<File>> getAvailableBackups() async {
    try {
      if (kIsWeb) return <File>[];

      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final Directory backupDir = Directory('${appDocDir.path}/backups');
      
      if (!await backupDir.exists()) {
        return <File>[];
      }

      final List<File> backupFiles = backupDir.listSync()
          .where((FileSystemEntity entity) => entity is File && entity.path.endsWith('.json'))
          .cast<File>()
          .toList();

      // Sort by modification time (newest first)
      backupFiles.sort((File a, File b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));

      return backupFiles;
    } catch (e) {
      return <File>[];
    }
  }

  /// Restores from a specific auto backup file
  Future<bool> restoreFromAutoBackup(File backupFile) async {
    try {
      final String jsonString = await backupFile.readAsString();
      return await restoreFromBackupData(jsonString);
    } catch (e) {
      throw Exception('Failed to restore from auto backup: $e');
    }
  }
}
