import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/filter_provider.dart';
import 'package:inventory_management_app/widgets/custom_app_bar.dart';
import 'package:inventory_management_app/widgets/custom_list_tile.dart';
import 'package:inventory_management_app/widgets/empty_state_widget.dart';
import 'package:inventory_management_app/models/product.dart';

class ProductsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final productProvider = Provider.of<ProductProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Products'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              context.go('/products/add');
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: productProvider.products.length,
        itemBuilder: (context, index) {
          final product = productProvider.products[index];
          return Card(
            child: ListTile(
              title: Text(product.name),
              subtitle: Text(product.description ?? 'No description'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      context.go('/products/edit/${product.id}');
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      productProvider.deleteProduct(product.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
