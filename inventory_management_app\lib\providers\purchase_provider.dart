import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/purchase.dart';
import 'package:inventory_management_app/services/database_service.dart';

class PurchaseProvider extends ChangeNotifier {
  List<Purchase> _purchases = [];
  final _databaseService = DatabaseService();

  List<Purchase> get purchases => _purchases;

  Future<void> fetchPurchases() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('purchases');
    _purchases = List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addPurchase(Purchase purchase) async {
    final db = await _databaseService.database;
    await db.insert('purchases', purchase.toMap());
    await fetchPurchases();
  }

  Future<void> updatePurchase(Purchase purchase) async {
    final db = await _databaseService.database;
    await db.update(
      'purchases',
      purchase.toMap(),
      where: 'id = ?',
      whereArgs: [purchase.id],
    );
    await fetchPurchases();
  }

  Future<void> deletePurchase(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'purchases',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchPurchases();
  }
}
