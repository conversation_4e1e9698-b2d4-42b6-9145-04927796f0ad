class Order {
  int? id;
  int customerId;
  DateTime orderDate;
  double totalAmount;

  Order({
    this.id,
    required this.customerId,
    required this.orderDate,
    required this.totalAmount,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerId': customerId,
      'orderDate': orderDate.toIso8601String(),
      'totalAmount': totalAmount,
    };
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      customerId: map['customerId'],
      orderDate: DateTime.parse(map['orderDate']),
      totalAmount: map['totalAmount'],
    );
  }
}
