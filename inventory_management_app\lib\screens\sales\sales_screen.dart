import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/screens/sales/sale_details_screen.dart';

class SalesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final saleProvider = Provider.of<SaleProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Sales'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              // TODO: Implement navigation to add sale screen
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: saleProvider.sales.length,
        itemBuilder: (context, index) {
          final sale = saleProvider.sales[index];
          return Card(
            child: ListTile(
              title: Text('Sale ID: ${sale.id}'),
              subtitle: Text('Customer ID: ${sale.customerId}'),
              trailing: Icon(Icons.arrow_forward),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SaleDetailsScreen(sale: sale),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
