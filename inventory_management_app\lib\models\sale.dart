class Sale {
  int? id;
  int customerId;
  DateTime saleDate;
  double totalAmount;

  Sale({
    this.id,
    required this.customerId,
    required this.saleDate,
    required this.totalAmount,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerId': customerId,
      'saleDate': saleDate.toIso8601String(),
      'totalAmount': totalAmount,
    };
  }

  factory Sale.fromMap(Map<String, dynamic> map) {
    return Sale(
      id: map['id'],
      customerId: map['customerId'],
      saleDate: DateTime.parse(map['saleDate']),
      totalAmount: map['totalAmount'],
    );
  }
}
