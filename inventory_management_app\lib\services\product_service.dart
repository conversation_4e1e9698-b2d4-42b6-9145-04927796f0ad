import 'package:sqflite/sqflite.dart';
import '../models/product.dart';
import 'database_service.dart';

/// Service class for handling Product CRUD operations
class ProductService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all products from the database
  Future<List<Product>> getAllProducts() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('products');
    
    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get a product by its ID
  Future<Product?> getProductById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new product into the database
  Future<int> insertProduct(Product product) async {
    final db = await _databaseService.database;
    return await db.insert(
      'products',
      product.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing product in the database
  Future<int> updateProduct(Product product) async {
    final db = await _databaseService.database;
    return await db.update(
      'products',
      product.toMap(),
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  /// Delete a product from the database
  Future<int> deleteProduct(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'products',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Search products by name
  Future<List<Product>> searchProductsByName(String name) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'name LIKE ?',
      whereArgs: ['%$name%'],
    );
    
    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get products by category
  Future<List<Product>> getProductsByCategory(int categoryId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'categoryId = ?',
      whereArgs: [categoryId],
    );
    
    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get products by supplier
  Future<List<Product>> getProductsBySupplier(int supplierId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'supplierId = ?',
      whereArgs: [supplierId],
    );
    
    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Get products with low stock (quantity below threshold)
  Future<List<Product>> getLowStockProducts(double threshold) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'products',
      where: 'quantity < ?',
      whereArgs: [threshold],
    );
    
    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// Update product quantity
  Future<int> updateProductQuantity(int productId, double newQuantity) async {
    final db = await _databaseService.database;
    return await db.update(
      'products',
      {'quantity': newQuantity},
      where: 'id = ?',
      whereArgs: [productId],
    );
  }

  /// Get total number of products
  Future<int> getProductCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM products');
    return result.first['count'] as int;
  }
}
