import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/services/settings_service.dart';
import 'package:inventory_management_app/providers/theme_provider.dart';
import 'package:inventory_management_app/widgets/custom_app_bar.dart';

/// Screen for managing app settings
class SettingsScreen extends StatefulWidget {
  /// Constructor for SettingsScreen
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsService _settingsService = SettingsService();

  // Settings values
  ThemeMode _themeMode = ThemeMode.system;
  String _language = 'en';
  String _currency = 'USD';
  bool _autoBackup = false;
  int _backupFrequency = 7;
  bool _notifications = true;
  bool _lowStockAlert = true;
  double _lowStockThreshold = 10.0;
  String _companyName = 'My Company';
  String _companyAddress = '';
  String _companyPhone = '';
  String _companyEmail = '';
  double _taxRate = 0.0;
  int _decimalPlaces = 2;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final ThemeMode themeMode = await _settingsService.getThemeMode();
      final String language = await _settingsService.getLanguage();
      final String currency = await _settingsService.getCurrency();
      final bool autoBackup = await _settingsService.getAutoBackupEnabled();
      final int backupFrequency = await _settingsService.getBackupFrequency();
      final bool notifications =
          await _settingsService.getNotificationsEnabled();
      final bool lowStockAlert =
          await _settingsService.getLowStockAlertEnabled();
      final double lowStockThreshold =
          await _settingsService.getLowStockThreshold();
      final String companyName = await _settingsService.getCompanyName();
      final String companyAddress = await _settingsService.getCompanyAddress();
      final String companyPhone = await _settingsService.getCompanyPhone();
      final String companyEmail = await _settingsService.getCompanyEmail();
      final double taxRate = await _settingsService.getTaxRate();
      final int decimalPlaces = await _settingsService.getDecimalPlaces();

      setState(() {
        _themeMode = themeMode;
        _language = language;
        _currency = currency;
        _autoBackup = autoBackup;
        _backupFrequency = backupFrequency;
        _notifications = notifications;
        _lowStockAlert = lowStockAlert;
        _lowStockThreshold = lowStockThreshold;
        _companyName = companyName;
        _companyAddress = companyAddress;
        _companyPhone = companyPhone;
        _companyEmail = companyEmail;
        _taxRate = taxRate;
        _decimalPlaces = decimalPlaces;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Failed to load settings: $e');
    }
  }

  Future<void> _updateThemeMode(ThemeMode? themeMode) async {
    if (themeMode != null) {
      await _settingsService.setThemeMode(themeMode);
      setState(() {
        _themeMode = themeMode;
      });
    }
  }

  Future<void> _updateCurrency(String? currency) async {
    if (currency != null) {
      await _settingsService.setCurrency(currency);
      setState(() {
        _currency = currency;
      });
    }
  }

  Future<void> _updateAutoBackup(bool value) async {
    await _settingsService.setAutoBackupEnabled(value);
    setState(() {
      _autoBackup = value;
    });
  }

  Future<void> _updateNotifications(bool value) async {
    await _settingsService.setNotificationsEnabled(value);
    setState(() {
      _notifications = value;
    });
  }

  Future<void> _updateLowStockAlert(bool value) async {
    await _settingsService.setLowStockAlertEnabled(value);
    setState(() {
      _lowStockAlert = value;
    });
  }

  Future<void> _updateLowStockThreshold(double value) async {
    await _settingsService.setLowStockThreshold(value);
    setState(() {
      _lowStockThreshold = value;
    });
  }

  Future<void> _updateCompanyInfo() async {
    await showDialog<void>(
      context: context,
      builder: (BuildContext context) => _CompanyInfoDialog(
        initialName: _companyName,
        initialAddress: _companyAddress,
        initialPhone: _companyPhone,
        initialEmail: _companyEmail,
        onSave:
            (String name, String address, String phone, String email) async {
          await _settingsService.setCompanyName(name);
          await _settingsService.setCompanyAddress(address);
          await _settingsService.setCompanyPhone(phone);
          await _settingsService.setCompanyEmail(email);
          setState(() {
            _companyName = name;
            _companyAddress = address;
            _companyPhone = phone;
            _companyEmail = email;
          });
        },
      ),
    );
  }

  Future<void> _resetSettings() async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
            'Are you sure you want to reset all settings to default values?'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _settingsService.resetAllSettings();
      await _loadSettings();
      _showSuccessSnackBar('Settings reset successfully');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Settings',
        showBackButton: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: <Widget>[
          _buildAppearanceSection(),
          const SizedBox(height: 24),
          _buildBackupSection(),
          const SizedBox(height: 24),
          _buildNotificationSection(),
          const SizedBox(height: 24),
          _buildCompanySection(),
          const SizedBox(height: 24),
          _buildFinancialSection(),
          const SizedBox(height: 24),
          _buildActionsSection(),
        ],
      ),
    );
  }

  Widget _buildAppearanceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Appearance',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.palette),
              title: const Text('Theme'),
              subtitle: Text(_getThemeModeText(_themeMode)),
              trailing: Consumer<ThemeProvider>(
                builder: (context, themeProvider, child) {
                  return DropdownButton<ThemeMode>(
                    value: themeProvider.themeMode,
                    onChanged: (ThemeMode? mode) {
                      if (mode != null) {
                        themeProvider.setThemeMode(mode);
                        setState(() {
                          _themeMode = mode;
                        });
                      }
                    },
                    items: const <DropdownMenuItem<ThemeMode>>[
                      DropdownMenuItem<ThemeMode>(
                        value: ThemeMode.system,
                        child: Text('System'),
                      ),
                      DropdownMenuItem<ThemeMode>(
                        value: ThemeMode.light,
                        child: Text('Light'),
                      ),
                      DropdownMenuItem<ThemeMode>(
                        value: ThemeMode.dark,
                        child: Text('Dark'),
                      ),
                    ],
                  );
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.attach_money),
              title: const Text('Currency'),
              subtitle: Text(_currency),
              trailing: DropdownButton<String>(
                value: _currency,
                onChanged: _updateCurrency,
                items: const <DropdownMenuItem<String>>[
                  DropdownMenuItem<String>(
                      value: 'USD', child: Text('USD (\$)')),
                  DropdownMenuItem<String>(
                      value: 'EUR', child: Text('EUR (€)')),
                  DropdownMenuItem<String>(
                      value: 'GBP', child: Text('GBP (£)')),
                  DropdownMenuItem<String>(
                      value: 'SAR', child: Text('SAR (ر.س)')),
                  DropdownMenuItem<String>(
                      value: 'AED', child: Text('AED (د.إ)')),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Backup & Restore',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              secondary: const Icon(Icons.backup),
              title: const Text('Auto Backup'),
              subtitle: const Text('Automatically create backups'),
              value: _autoBackup,
              onChanged: _updateAutoBackup,
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('Backup Frequency'),
              subtitle: Text('Every $_backupFrequency days'),
              trailing: DropdownButton<int>(
                value: _backupFrequency,
                onChanged: (int? value) async {
                  if (value != null) {
                    await _settingsService.setBackupFrequency(value);
                    setState(() {
                      _backupFrequency = value;
                    });
                  }
                },
                items: const <DropdownMenuItem<int>>[
                  DropdownMenuItem<int>(value: 1, child: Text('Daily')),
                  DropdownMenuItem<int>(value: 7, child: Text('Weekly')),
                  DropdownMenuItem<int>(value: 30, child: Text('Monthly')),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.cloud_upload),
              title: const Text('Manage Backups'),
              subtitle: const Text('Create or restore backups'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.go('/backup'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Notifications',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              secondary: const Icon(Icons.notifications),
              title: const Text('Enable Notifications'),
              subtitle: const Text('Receive app notifications'),
              value: _notifications,
              onChanged: _updateNotifications,
            ),
            SwitchListTile(
              secondary: const Icon(Icons.warning),
              title: const Text('Low Stock Alerts'),
              subtitle: const Text('Alert when products are low in stock'),
              value: _lowStockAlert,
              onChanged: _updateLowStockAlert,
            ),
            ListTile(
              leading: const Icon(Icons.inventory),
              title: const Text('Low Stock Threshold'),
              subtitle: Text(
                  'Alert when quantity is below ${_lowStockThreshold.toInt()}'),
              trailing: SizedBox(
                width: 100,
                child: TextFormField(
                  initialValue: _lowStockThreshold.toInt().toString(),
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  onFieldSubmitted: (String value) {
                    final double? newValue = double.tryParse(value);
                    if (newValue != null && newValue >= 0) {
                      _updateLowStockThreshold(newValue);
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Company Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.business),
              title: const Text('Company Details'),
              subtitle: Text(_companyName),
              trailing: const Icon(Icons.edit),
              onTap: _updateCompanyInfo,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Financial Settings',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.percent),
              title: const Text('Tax Rate'),
              subtitle: Text('${_taxRate.toStringAsFixed(1)}%'),
              trailing: SizedBox(
                width: 100,
                child: TextFormField(
                  initialValue: _taxRate.toString(),
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  onFieldSubmitted: (String value) async {
                    final double? newValue = double.tryParse(value);
                    if (newValue != null && newValue >= 0 && newValue <= 100) {
                      await _settingsService.setTaxRate(newValue);
                      setState(() {
                        _taxRate = newValue;
                      });
                    }
                  },
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.decimal_increase),
              title: const Text('Decimal Places'),
              subtitle: Text('$_decimalPlaces places'),
              trailing: DropdownButton<int>(
                value: _decimalPlaces,
                onChanged: (int? value) async {
                  if (value != null) {
                    await _settingsService.setDecimalPlaces(value);
                    setState(() {
                      _decimalPlaces = value;
                    });
                  }
                },
                items: const <DropdownMenuItem<int>>[
                  DropdownMenuItem<int>(value: 0, child: Text('0')),
                  DropdownMenuItem<int>(value: 1, child: Text('1')),
                  DropdownMenuItem<int>(value: 2, child: Text('2')),
                  DropdownMenuItem<int>(value: 3, child: Text('3')),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.analytics),
              title: const Text('Reports'),
              subtitle: const Text('View reports and analytics'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => context.go('/reports'),
            ),
            ListTile(
              leading: const Icon(Icons.restore, color: Colors.orange),
              title: const Text('Reset Settings'),
              subtitle: const Text('Reset all settings to default'),
              onTap: _resetSettings,
            ),
          ],
        ),
      ),
    );
  }

  String _getThemeModeText(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }
}

class _CompanyInfoDialog extends StatefulWidget {
  const _CompanyInfoDialog({
    required this.initialName,
    required this.initialAddress,
    required this.initialPhone,
    required this.initialEmail,
    required this.onSave,
  });

  final String initialName;
  final String initialAddress;
  final String initialPhone;
  final String initialEmail;
  final Function(String, String, String, String) onSave;

  @override
  State<_CompanyInfoDialog> createState() => _CompanyInfoDialogState();
}

class _CompanyInfoDialogState extends State<_CompanyInfoDialog> {
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialName);
    _addressController = TextEditingController(text: widget.initialAddress);
    _phoneController = TextEditingController(text: widget.initialPhone);
    _emailController = TextEditingController(text: widget.initialEmail);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Company Information'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Company Name',
                prefixIcon: Icon(Icons.business),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onSave(
              _nameController.text,
              _addressController.text,
              _phoneController.text,
              _emailController.text,
            );
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
