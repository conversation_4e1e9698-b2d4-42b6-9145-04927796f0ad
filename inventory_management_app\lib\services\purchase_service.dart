import 'package:sqflite/sqflite.dart';
import '../models/purchase.dart';
import '../models/purchase_item.dart';
import 'database_service.dart';

/// Service class for handling Purchase CRUD operations
class PurchaseService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all purchases from the database
  Future<List<Purchase>> getAllPurchases() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('purchases');
    
    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// Get a purchase by its ID
  Future<Purchase?> getPurchaseById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchases',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Purchase.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new purchase into the database
  Future<int> insertPurchase(Purchase purchase) async {
    final db = await _databaseService.database;
    return await db.insert(
      'purchases',
      purchase.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing purchase in the database
  Future<int> updatePurchase(Purchase purchase) async {
    final db = await _databaseService.database;
    return await db.update(
      'purchases',
      purchase.toMap(),
      where: 'id = ?',
      whereArgs: [purchase.id],
    );
  }

  /// Delete a purchase from the database
  Future<int> deletePurchase(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'purchases',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Get purchases by supplier
  Future<List<Purchase>> getPurchasesBySupplier(int supplierId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchases',
      where: 'supplierId = ?',
      whereArgs: [supplierId],
    );
    
    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// Get purchases by date range
  Future<List<Purchase>> getPurchasesByDateRange(String startDate, String endDate) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchases',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [startDate, endDate],
    );
    
    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// Get total purchases amount
  Future<double> getTotalPurchasesAmount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT SUM(total) as total FROM purchases');
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// Get total number of purchases
  Future<int> getPurchaseCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM purchases');
    return result.first['count'] as int;
  }

  /// Get purchase items for a specific purchase
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'purchase_items',
      where: 'purchaseId = ?',
      whereArgs: [purchaseId],
    );
    
    return List.generate(maps.length, (i) {
      return PurchaseItem.fromMap(maps[i]);
    });
  }

  /// Insert purchase item
  Future<int> insertPurchaseItem(PurchaseItem purchaseItem) async {
    final db = await _databaseService.database;
    return await db.insert(
      'purchase_items',
      purchaseItem.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Delete purchase items for a specific purchase
  Future<int> deletePurchaseItems(int purchaseId) async {
    final db = await _databaseService.database;
    return await db.delete(
      'purchase_items',
      where: 'purchaseId = ?',
      whereArgs: [purchaseId],
    );
  }
}
