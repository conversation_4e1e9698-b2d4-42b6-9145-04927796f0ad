import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/services/database_service.dart';

class SaleProvider extends ChangeNotifier {
  List<Sale> _sales = [];
  final _databaseService = DatabaseService();

  List<Sale> get sales => _sales;

  Future<void> fetchSales() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('sales');
    _sales = List.generate(maps.length, (i) {
      return Sale.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addSale(Sale sale) async {
    final db = await _databaseService.database;
    await db.insert('sales', sale.toMap());
    await fetchSales();
  }

  Future<void> updateSale(Sale sale) async {
    final db = await _databaseService.database;
    await db.update(
      'sales',
      sale.toMap(),
      where: 'id = ?',
      whereArgs: [sale.id],
    );
    await fetchSales();
  }

  Future<void> deleteSale(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'sales',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchSales();
  }
}
