import 'package:flutter/material.dart';
import 'package:inventory_management_app/utils/validators.dart';
import 'package:inventory_management_app/services/business_logic_service.dart';

/// Provider class for managing validation across the application
class ValidationProvider extends ChangeNotifier {
  final BusinessLogicService _businessLogic = BusinessLogicService();
  
  // Validation states
  Map<String, String?> _validationErrors = <String, String?>{};
  bool _isValidating = false;

  /// Get validation errors
  Map<String, String?> get validationErrors => _validationErrors;
  
  /// Get validation state
  bool get isValidating => _isValidating;

  /// Validate required field
  String? validateRequired(String? value, String fieldName) {
    final error = Validators.required(value, fieldName);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate email format
  String? validateEmail(String? value, String fieldName) {
    final error = Validators.email(value);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate phone number
  String? validatePhone(String? value, String fieldName) {
    final error = Validators.phone(value);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate positive number
  String? validatePositiveNumber(String? value, String fieldName) {
    final error = Validators.positiveNumber(value, fieldName);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate non-negative number
  String? validateNonNegativeNumber(String? value, String fieldName) {
    final error = Validators.nonNegativeNumber(value, fieldName);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate minimum length
  String? validateMinLength(String? value, int minLength, String fieldName) {
    final error = Validators.minLength(value, minLength, fieldName);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate maximum length
  String? validateMaxLength(String? value, int maxLength, String fieldName) {
    final error = Validators.maxLength(value, maxLength, fieldName);
    _setValidationError(fieldName, error);
    return error;
  }

  /// Validate product name uniqueness
  Future<String?> validateProductNameUniqueness(
    String name, 
    int? categoryId, 
    {int? excludeId}
  ) async {
    _setValidating(true);
    
    try {
      final isUnique = await _businessLogic.isProductNameUniqueInCategory(
          name, categoryId, excludeProductId: excludeId);
      
      final error = isUnique ? null : 'Product name already exists in this category';
      _setValidationError('productName', error);
      return error;
    } catch (e) {
      final error = 'Failed to validate product name: $e';
      _setValidationError('productName', error);
      return error;
    } finally {
      _setValidating(false);
    }
  }

  /// Validate customer email uniqueness
  Future<String?> validateCustomerEmailUniqueness(
    String email, 
    {int? excludeId}
  ) async {
    _setValidating(true);
    
    try {
      final isUnique = await _businessLogic.isCustomerEmailUnique(
          email, excludeCustomerId: excludeId);
      
      final error = isUnique ? null : 'Email already exists';
      _setValidationError('customerEmail', error);
      return error;
    } catch (e) {
      final error = 'Failed to validate email: $e';
      _setValidationError('customerEmail', error);
      return error;
    } finally {
      _setValidating(false);
    }
  }

  /// Validate supplier email uniqueness
  Future<String?> validateSupplierEmailUniqueness(
    String email, 
    {int? excludeId}
  ) async {
    _setValidating(true);
    
    try {
      final isUnique = await _businessLogic.isSupplierEmailUnique(
          email, excludeSupplierId: excludeId);
      
      final error = isUnique ? null : 'Email already exists';
      _setValidationError('supplierEmail', error);
      return error;
    } catch (e) {
      final error = 'Failed to validate email: $e';
      _setValidationError('supplierEmail', error);
      return error;
    } finally {
      _setValidating(false);
    }
  }

  /// Validate stock availability for sale
  Future<String?> validateStockAvailability(
    int productId, 
    double requestedQuantity
  ) async {
    _setValidating(true);
    
    try {
      final hasStock = await _businessLogic.hasEnoughStock(
          productId, requestedQuantity);
      
      final error = hasStock ? null : 'Insufficient stock available';
      _setValidationError('stockQuantity', error);
      return error;
    } catch (e) {
      final error = 'Failed to validate stock: $e';
      _setValidationError('stockQuantity', error);
      return error;
    } finally {
      _setValidating(false);
    }
  }

  /// Validate deletion constraints
  Future<String?> validateDeletion(String entityType, int entityId) async {
    _setValidating(true);
    
    try {
      final result = await _businessLogic.validateDeletion(entityType, entityId);
      final error = result['canDelete'] ? null : result['reason'];
      _setValidationError('deletion', error);
      return error;
    } catch (e) {
      final error = 'Failed to validate deletion: $e';
      _setValidationError('deletion', error);
      return error;
    } finally {
      _setValidating(false);
    }
  }

  /// Validate form with multiple fields
  Future<bool> validateForm(Map<String, dynamic> formData) async {
    clearValidationErrors();
    bool isValid = true;

    // Validate each field based on its type and requirements
    for (final entry in formData.entries) {
      final fieldName = entry.key;
      final value = entry.value;

      if (fieldName.contains('email')) {
        final error = validateEmail(value?.toString(), fieldName);
        if (error != null) isValid = false;
      } else if (fieldName.contains('phone')) {
        final error = validatePhone(value?.toString(), fieldName);
        if (error != null) isValid = false;
      } else if (fieldName.contains('price') || fieldName.contains('amount')) {
        final error = validatePositiveNumber(value?.toString(), fieldName);
        if (error != null) isValid = false;
      } else if (fieldName.contains('quantity')) {
        final error = validateNonNegativeNumber(value?.toString(), fieldName);
        if (error != null) isValid = false;
      } else if (fieldName.contains('name') || fieldName.contains('title')) {
        final error = validateRequired(value?.toString(), fieldName);
        if (error != null) isValid = false;
      }
    }

    return isValid;
  }

  /// Set validation error for a specific field
  void _setValidationError(String fieldName, String? error) {
    if (error != null) {
      _validationErrors[fieldName] = error;
    } else {
      _validationErrors.remove(fieldName);
    }
    notifyListeners();
  }

  /// Set validation state
  void _setValidating(bool validating) {
    _isValidating = validating;
    notifyListeners();
  }

  /// Clear validation error for a specific field
  void clearValidationError(String fieldName) {
    _validationErrors.remove(fieldName);
    notifyListeners();
  }

  /// Clear all validation errors
  void clearValidationErrors() {
    _validationErrors.clear();
    notifyListeners();
  }

  /// Check if form has any validation errors
  bool get hasValidationErrors => _validationErrors.isNotEmpty;

  /// Get validation error for a specific field
  String? getValidationError(String fieldName) {
    return _validationErrors[fieldName];
  }

  /// Get all validation errors as a list
  List<String> getAllValidationErrors() {
    return _validationErrors.values.where((error) => error != null).cast<String>().toList();
  }
}
