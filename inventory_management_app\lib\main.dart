import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/core/app_router.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/providers/order_provider.dart';
import 'package:inventory_management_app/providers/customer_provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/providers/sale_provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/providers/expense_provider.dart';
import 'package:inventory_management_app/providers/category_provider.dart';
import 'package:inventory_management_app/providers/unit_provider.dart';
import 'package:inventory_management_app/providers/reports_provider.dart';
import 'package:inventory_management_app/services/settings_service.dart';
import 'package:inventory_management_app/providers/theme_provider.dart';
import 'package:inventory_management_app/providers/filter_provider.dart';
import 'package:inventory_management_app/providers/validation_provider.dart';
import 'package:inventory_management_app/utils/theme.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize FFI
  if (kIsWeb) {
    databaseFactory = databaseFactoryFfiWeb;
  } else {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize settings service
  await SettingsService().init();

  runApp(
    MultiProvider(
      providers: <ChangeNotifierProvider<dynamic>>[
        ChangeNotifierProvider<ProductProvider>(
            create: (_) => ProductProvider()),
        ChangeNotifierProvider<OrderProvider>(create: (_) => OrderProvider()),
        ChangeNotifierProvider<CustomerProvider>(
            create: (_) => CustomerProvider()),
        ChangeNotifierProvider<SupplierProvider>(
            create: (_) => SupplierProvider()),
        ChangeNotifierProvider<SaleProvider>(create: (_) => SaleProvider()),
        ChangeNotifierProvider<PurchaseProvider>(
            create: (_) => PurchaseProvider()),
        ChangeNotifierProvider<ExpenseProvider>(
            create: (_) => ExpenseProvider()),
        ChangeNotifierProvider<CategoryProvider>(
            create: (_) => CategoryProvider()),
        ChangeNotifierProvider<UnitProvider>(create: (_) => UnitProvider()),
        ChangeNotifierProvider<ReportsProvider>(
            create: (_) => ReportsProvider()),
        ChangeNotifierProvider<FilterProvider>(create: (_) => FilterProvider()),
        ChangeNotifierProvider<ValidationProvider>(
            create: (_) => ValidationProvider()),
        ChangeNotifierProvider<ThemeProvider>(create: (_) => ThemeProvider()),
      ],
      child: MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    Provider.of<ProductProvider>(context, listen: false).fetchProducts();
    Provider.of<OrderProvider>(context, listen: false).fetchOrders();
    Provider.of<CustomerProvider>(context, listen: false).fetchCustomers();
    Provider.of<SupplierProvider>(context, listen: false).fetchSuppliers();
    Provider.of<SaleProvider>(context, listen: false).fetchSales();
    Provider.of<PurchaseProvider>(context, listen: false).fetchPurchases();
    Provider.of<ExpenseProvider>(context, listen: false).fetchExpenses();
    Provider.of<CategoryProvider>(context, listen: false).fetchCategories();
    Provider.of<UnitProvider>(context, listen: false).fetchUnits();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder:
          (BuildContext context, ThemeProvider themeProvider, Widget? child) {
        return MaterialApp.router(
          title: 'إدارة محل المواد الغذائية',
          routerConfig: AppRouter.router,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeProvider.themeMode,
          locale: const Locale('ar', 'SA'),
          supportedLocales: const <Locale>[
            Locale('ar', 'SA'),
            Locale('en', 'US'),
          ],
        );
      },
    );
  }
}
