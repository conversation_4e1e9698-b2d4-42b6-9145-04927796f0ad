/// Model class representing a business expense
class Expense {
  /// Unique identifier for the expense
  int? id;

  /// Category ID for this expense
  int? categoryId;

  /// Amount of the expense
  double? amount;

  /// Date of the expense
  String? date;

  /// Additional notes for the expense
  String? notes;

  /// Constructor for creating an Expense instance
  Expense({
    this.id,
    this.categoryId,
    this.amount,
    this.date,
    this.notes,
  });

  /// Converts the Expense instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'categoryId': categoryId,
      'amount': amount,
      'date': date,
      'notes': notes,
    };
  }

  /// Creates an Expense instance from a Map (typically from database)
  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'] as int?,
      categoryId: map['categoryId'] as int?,
      amount: map['amount']?.toDouble(),
      date: map['date'] as String?,
      notes: map['notes'] as String?,
    );
  }

  @override
  String toString() {
    return 'Expense{id: $id, categoryId: $categoryId, amount: $amount, '
        'date: $date, notes: $notes}';
  }
}
