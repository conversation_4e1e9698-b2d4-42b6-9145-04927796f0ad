class Expense {
  int? id;
  DateTime expenseDate;
  String category;
  double amount;
  String description;

  Expense({
    this.id,
    required this.expenseDate,
    required this.category,
    required this.amount,
    required this.description,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'expenseDate': expenseDate.toIso8601String(),
      'category': category,
      'amount': amount,
      'description': description,
    };
  }

  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'],
      expenseDate: DateTime.parse(map['expenseDate']),
      category: map['category'],
      amount: map['amount'],
      description: map['description'],
    );
  }
}
