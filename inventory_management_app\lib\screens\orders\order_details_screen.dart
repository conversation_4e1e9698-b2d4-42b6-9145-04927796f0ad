import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/order.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/models/order_item.dart';

class OrderDetailsScreen extends StatefulWidget {
  final Order order;

  OrderDetailsScreen({required this.order});

  @override
  _OrderDetailsScreenState createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  List<OrderItem> _orderItems = [];

  @override
  void initState() {
    super.initState();
    _fetchOrderItems();
  }

  Future<void> _fetchOrderItems() async {
    final db = await DatabaseService().database;
    final List<Map<String, dynamic>> maps = await db.query(
      'order_items',
      where: 'orderId = ?',
      whereArgs: [widget.order.id],
    );
    setState(() {
      _orderItems = List.generate(maps.length, (i) {
        return OrderItem.fromMap(maps[i]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Order Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order ID: ${widget.order.id}'),
            Text('Customer ID: ${widget.order.customerId}'),
            Text('Order Date: ${widget.order.orderDate}'),
            Text(
                'Total Amount: \$${widget.order.totalAmount.toStringAsFixed(2)}'),
            SizedBox(height: 16),
            Text(
              'Order Items:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: _orderItems.length,
                itemBuilder: (context, index) {
                  final orderItem = _orderItems[index];
                  return ListTile(
                    title: Text('Product ID: ${orderItem.productId}'),
                    subtitle: Text(
                        'Quantity: ${orderItem.quantity}, Price: \$${orderItem.price.toStringAsFixed(2)}'),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
