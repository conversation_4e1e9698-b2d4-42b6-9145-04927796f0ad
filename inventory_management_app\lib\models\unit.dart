class Unit {
  int? id;
  String name;
  String symbol;

  Unit({
    this.id,
    required this.name,
    required this.symbol,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'symbol': symbol,
    };
  }

  factory Unit.fromMap(Map<String, dynamic> map) {
    return Unit(
      id: map['id'],
      name: map['name'],
      symbol: map['symbol'],
    );
  }
}
