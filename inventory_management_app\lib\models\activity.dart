class Activity {
  int? id;
  String description;
  DateTime activityDate;

  Activity({
    this.id,
    required this.description,
    required this.activityDate,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'description': description,
      'activityDate': activityDate.toIso8601String(),
    };
  }

  factory Activity.fromMap(Map<String, dynamic> map) {
    return Activity(
      id: map['id'],
      description: map['description'],
      activityDate: DateTime.parse(map['activityDate']),
    );
  }
}
