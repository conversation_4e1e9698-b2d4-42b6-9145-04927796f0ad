import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/order.dart';
import 'package:inventory_management_app/services/database_service.dart';

class OrderProvider extends ChangeNotifier {
  List<Order> _orders = [];
  final _databaseService = DatabaseService();

  List<Order> get orders => _orders;

  Future<void> fetchOrders() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('orders');
    _orders = List.generate(maps.length, (i) {
      return Order.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addOrder(Order order) async {
    final db = await _databaseService.database;
    await db.insert('orders', order.toMap());
    await fetchOrders();
  }
}
