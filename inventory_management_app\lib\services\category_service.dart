import 'package:sqflite/sqflite.dart';
import '../models/category.dart';
import 'database_service.dart';

/// Service class for handling Category CRUD operations
class CategoryService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all categories from the database
  Future<List<Category>> getAllCategories() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('categories');
    
    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// Get a category by its ID
  Future<Category?> getCategoryById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Category.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new category into the database
  Future<int> insertCategory(Category category) async {
    final db = await _databaseService.database;
    return await db.insert(
      'categories',
      category.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing category in the database
  Future<int> updateCategory(Category category) async {
    final db = await _databaseService.database;
    return await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  /// Delete a category from the database
  Future<int> deleteCategory(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Search categories by name
  Future<List<Category>> searchCategoriesByName(String name) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: 'name LIKE ?',
      whereArgs: ['%$name%'],
    );
    
    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// Get total number of categories
  Future<int> getCategoryCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM categories');
    return result.first['count'] as int;
  }

  /// Check if category name already exists
  Future<bool> categoryNameExists(String name, {int? excludeId}) async {
    final db = await _databaseService.database;
    String whereClause = 'name = ?';
    List<dynamic> whereArgs = [name];
    
    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final List<Map<String, dynamic>> maps = await db.query(
      'categories',
      where: whereClause,
      whereArgs: whereArgs,
    );
    
    return maps.isNotEmpty;
  }
}
