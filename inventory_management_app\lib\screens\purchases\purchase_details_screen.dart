import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/purchase_provider.dart';
import 'package:inventory_management_app/models/purchase.dart';

class PurchaseDetailsScreen extends StatefulWidget {
  final Purchase? purchase;

  PurchaseDetailsScreen({this.purchase});

  @override
  _PurchaseDetailsScreenState createState() => _PurchaseDetailsScreenState();
}

class _PurchaseDetailsScreenState extends State<PurchaseDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  late int _supplierId;
  late DateTime _purchaseDate;
  late double _totalAmount;

  @override
  void initState() {
    super.initState();
    _supplierId = widget.purchase?.supplierId ?? 0;
    _purchaseDate = widget.purchase?.purchaseDate ?? DateTime.now();
    _totalAmount = widget.purchase?.totalAmount ?? 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.purchase == null ? 'Add Purchase' : 'Edit Purchase'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                initialValue: _supplierId.toString(),
                decoration: InputDecoration(labelText: 'Supplier ID'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a Supplier ID';
                  }
                  return null;
                },
                onSaved: (value) => _supplierId = int.parse(value!),
              ),
              TextFormField(
                initialValue: _purchaseDate.toString(),
                decoration: InputDecoration(labelText: 'Purchase Date'),
                onSaved: (value) => _purchaseDate = DateTime.parse(value!),
              ),
              TextFormField(
                initialValue: _totalAmount.toString(),
                decoration: InputDecoration(labelText: 'Total Amount'),
                keyboardType: TextInputType.number,
                onSaved: (value) => _totalAmount = double.parse(value!),
              ),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();
                    final purchase = Purchase(
                      id: widget.purchase?.id,
                      supplierId: _supplierId,
                      purchaseDate: _purchaseDate,
                      totalAmount: _totalAmount,
                    );
                    final purchaseProvider =
                        Provider.of<PurchaseProvider>(context, listen: false);
                    if (widget.purchase == null) {
                      purchaseProvider.addPurchase(purchase);
                    } else {
                      purchaseProvider.updatePurchase(purchase);
                    }
                    Navigator.pop(context);
                  }
                },
                child: Text('Save'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
