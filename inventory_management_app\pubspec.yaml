name: inventory_management_app
description: A Flutter app for managing inventory with SQLite integration and state management.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  provider: ^6.0.0
  sqflite: ^2.0.0+4
  path: ^1.8.0
  go_router: ^13.0.0
  shared_preferences: ^2.0.0
  file_picker: ^6.0.0
  sqflite_common_ffi: ^2.0.0
  sqflite_common_ffi_web: ^1.0.0
  fl_chart: ^0.66.0
  permission_handler: ^11.0.0
  path_provider: ^2.1.0
  intl: ^0.20.0

  # googleapis: ^11.0.0   # (اختياري للمزامنة مع Google Drive)

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf