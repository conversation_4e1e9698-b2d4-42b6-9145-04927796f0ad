import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_management_app/providers/supplier_provider.dart';
import 'package:inventory_management_app/models/supplier.dart';
import 'package:inventory_management_app/screens/suppliers/supplier_details_screen.dart';

class SuppliersScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final supplierProvider = Provider.of<SupplierProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Suppliers'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SupplierDetailsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: supplierProvider.suppliers.length,
        itemBuilder: (context, index) {
          final supplier = supplierProvider.suppliers[index];
          return Card(
            child: ListTile(
              title: Text(supplier.name),
              subtitle: Text(supplier.email ?? ''),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.edit),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              SupplierDetailsScreen(supplier: supplier),
                        ),
                      );
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      supplierProvider.deleteSupplier(supplier.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
