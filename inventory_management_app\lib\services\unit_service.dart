import 'package:sqflite/sqflite.dart';
import '../models/unit.dart';
import 'database_service.dart';

/// Service class for handling Unit CRUD operations
class UnitService {
  final DatabaseService _databaseService = DatabaseService();

  /// Get all units from the database
  Future<List<Unit>> getAllUnits() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('units');
    
    return List.generate(maps.length, (i) {
      return Unit.fromMap(maps[i]);
    });
  }

  /// Get a unit by its ID
  Future<Unit?> getUnitById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'units',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Unit.fromMap(maps.first);
    }
    return null;
  }

  /// Insert a new unit into the database
  Future<int> insertUnit(Unit unit) async {
    final db = await _databaseService.database;
    return await db.insert(
      'units',
      unit.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Update an existing unit in the database
  Future<int> updateUnit(Unit unit) async {
    final db = await _databaseService.database;
    return await db.update(
      'units',
      unit.toMap(),
      where: 'id = ?',
      whereArgs: [unit.id],
    );
  }

  /// Delete a unit from the database
  Future<int> deleteUnit(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'units',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Search units by name
  Future<List<Unit>> searchUnitsByName(String name) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'units',
      where: 'name LIKE ?',
      whereArgs: ['%$name%'],
    );
    
    return List.generate(maps.length, (i) {
      return Unit.fromMap(maps[i]);
    });
  }

  /// Get total number of units
  Future<int> getUnitCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM units');
    return result.first['count'] as int;
  }

  /// Check if unit name already exists
  Future<bool> unitNameExists(String name, {int? excludeId}) async {
    final db = await _databaseService.database;
    String whereClause = 'name = ?';
    List<dynamic> whereArgs = [name];
    
    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final List<Map<String, dynamic>> maps = await db.query(
      'units',
      where: whereClause,
      whereArgs: whereArgs,
    );
    
    return maps.isNotEmpty;
  }
}
