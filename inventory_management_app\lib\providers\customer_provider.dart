import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/services/database_service.dart';

class CustomerProvider extends ChangeNotifier {
  List<Customer> _customers = [];
  final _databaseService = DatabaseService();

  List<Customer> get customers => _customers;

  Future<void> fetchCustomers() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query('customers');
    _customers = List.generate(maps.length, (i) {
      return Customer.fromMap(maps[i]);
    });
    notifyListeners();
  }

  Future<void> addCustomer(Customer customer) async {
    final db = await _databaseService.database;
    await db.insert('customers', customer.toMap());
    await fetchCustomers();
  }

  Future<void> updateCustomer(Customer customer) async {
    final db = await _databaseService.database;
    await db.update(
      'customers',
      customer.toMap(),
      where: 'id = ?',
      whereArgs: [customer.id],
    );
    await fetchCustomers();
  }

  Future<void> deleteCustomer(int id) async {
    final db = await _databaseService.database;
    await db.delete(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
    await fetchCustomers();
  }
}
