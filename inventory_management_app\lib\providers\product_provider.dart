import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/data/product_repository.dart';

class ProductProvider extends ChangeNotifier {
  List<Product> _products = [];
  final _productRepository = ProductRepository();

  List<Product> get products => _products;

  Future<void> fetchProducts() async {
    _products = await _productRepository.getProducts();
    notifyListeners();
  }

  Future<void> addProduct(Product product) async {
    await _productRepository.insertProduct(product);
    await fetchProducts();
  }

  Future<void> updateProduct(Product product) async {
    await _productRepository.updateProduct(product);
    await fetchProducts();
  }

  Future<void> deleteProduct(int id) async {
    await _productRepository.deleteProduct(id);
    await fetchProducts();
  }
}
