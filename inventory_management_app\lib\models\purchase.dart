/// Model class representing a purchase transaction
class Purchase {
  /// Unique identifier for the purchase
  int? id;

  /// Supplier ID for this purchase
  int? supplierId;

  /// Date of the purchase
  String? date;

  /// Total amount of the purchase
  double? total;

  /// Additional notes for the purchase
  String? notes;

  /// Constructor for creating a Purchase instance
  Purchase({
    this.id,
    this.supplierId,
    this.date,
    this.total,
    this.notes,
  });

  /// Converts the Purchase instance to a Map for database operations
  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'supplierId': supplierId,
      'date': date,
      'total': total,
      'notes': notes,
    };
  }

  /// Creates a Purchase instance from a Map (typically from database)
  factory Purchase.fromMap(Map<String, dynamic> map) {
    return Purchase(
      id: map['id'] as int?,
      supplierId: map['supplierId'] as int?,
      date: map['date'] as String?,
      total: map['total']?.toDouble(),
      notes: map['notes'] as String?,
    );
  }

  @override
  String toString() {
    return 'Purchase{id: $id, supplierId: $supplierId, date: $date, '
        'total: $total, notes: $notes}';
  }
}
