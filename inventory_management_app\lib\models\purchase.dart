class Purchase {
  int? id;
  int supplierId;
  DateTime purchaseDate;
  double totalAmount;

  Purchase({
    this.id,
    required this.supplierId,
    required this.purchaseDate,
    required this.totalAmount,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'supplierId': supplierId,
      'purchaseDate': purchaseDate.toIso8601String(),
      'totalAmount': totalAmount,
    };
  }

  factory Purchase.fromMap(Map<String, dynamic> map) {
    return Purchase(
      id: map['id'],
      supplierId: map['supplierId'],
      purchaseDate: DateTime.parse(map['purchaseDate']),
      totalAmount: map['totalAmount'],
    );
  }
}
